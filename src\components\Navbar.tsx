import React, { useEffect, useState } from 'react';
import Button from './Button';
import './Navbar.css';

interface NavbarProps {
  onJoinWaitlist?: () => void;
}

const Navigation = () => {
  return (
    <div className="navbar__nav">
      <a href="/" className="navbar__nav-link">Home</a>
      <span className="navbar__nav-separator"> • </span>
      <a href="/who-we-are" className="navbar__nav-link">Who we are</a>
      <span className="navbar__nav-separator"> • </span>
      <a href="/contact-us" className="navbar__nav-link">Contact Us</a>
    </div>
  );
};

export default function Navbar({ onJoinWaitlist }: NavbarProps) {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const threshold = 80; // Scroll threshold in pixels

      if (scrollPosition > threshold) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);

    // Cleanup function to remove event listener
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <nav className={`navbar ${isScrolled ? 'navbar--scrolled' : ''}`}>
      <div className="navbar__brand">
        LawVriksh
      </div>

      <div className="navbar__content">
        <Navigation />

        <Button onClick={onJoinWaitlist}>
          Join Waitlist
        </Button>
      </div>
    </nav>
  );
}
