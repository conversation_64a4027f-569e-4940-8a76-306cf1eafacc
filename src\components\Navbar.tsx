import React from 'react';
import Button from './Button';
import './Navbar.css';

interface NavbarProps {
  onJoinWaitlist?: () => void;
}

const Navigation = () => {
  return (
    <div className="navbar__nav">
      <a href="/" className="navbar__nav-link">Home</a>
      <span className="navbar__nav-separator"> • </span>
      <a href="/who-we-are" className="navbar__nav-link">Who we are</a>
      <span className="navbar__nav-separator"> • </span>
      <a href="/contact-us" className="navbar__nav-link">Contact Us</a>
    </div>
  );
};

export default function Navbar({ onJoinWaitlist }: NavbarProps) {

  return (
    <nav className="navbar">
      <div className="navbar__brand">
        LawVriksh
      </div>
      
      <div className="navbar__content">
        <Navigation />
        
        <Button onClick={onJoinWaitlist}>
          Join Waitlist
        </Button>
      </div>
    </nav>
  );
}
