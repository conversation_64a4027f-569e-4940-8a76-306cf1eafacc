@import url("https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Josefin+Sans:ital,wght@0,400;1,400&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Battambang:wght@400;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Barlow+Condensed:wght@300;400;500;600;700&display=swap");

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

body {
  overflow-x: hidden;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Can<PERSON>ell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

/* Ensure each section takes exactly 100vh */
.homepage {
  background-color: #fdfbf4;
  position: relative;
  display: flex;
  justify-content: space-between;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.homepage__features-section {
  display: flex;

  flex-direction: column;
  gap: 40px;

  width: fit-content;
}

.homepage__feature-item {
  display: flex;
  position: relative;
  gap: 20px;
  align-items: center;
}

.homepage__feature-icon {
  position: relative;
  height: 75px;
  width: 59px;
}

.homepage__feature-icon--medium {
  height: 63px;
  width: 59px;
}

.homepage__feature-icon--small {
  height: 59px;
  width: 59px;
}

.homepage__feature-content {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  width: fit-content;
}

.homepage__feature-content--wide {
  width: fit-content;
}

.homepage__feature-title {
  position: relative;
  align-self: stretch;
  font-family: "Battambang", sans-serif;
  font-size: 24px;
  letter-spacing: -0.05em;
  line-height: 24px;
}

.homepage__feature-description {
  position: relative;
  align-self: stretch;
  font-family: "Source Sans Pro", sans-serif;
  font-size: 18px;
  letter-spacing: -0.05em;
  line-height: 20px;
  color: #966f33;
}
.homepage__hero-title {
  flex-shrink: 0;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 96px;
  color: #5D4037;
  height: 327px;
  line-height: 115.83px;
  left: 67px;
  top: 133px;
  width: 892px;
}
.homepage__hero {
  background-color: #fefaef;

  display: flex;
  justify-content: center;
  align-items: end;
  padding-bottom: 87px;
  height: 100vh;
  width: 70vw;
  box-shadow: 0px 4px 47.7px 0px rgba(0, 0, 0, 0.25);
}
.homepage__hero1 {
  background-color: #fefaef;

  display: flex;
  justify-content: center;
  align-items: center;

  height: 100vh;
  width: 40vw;
  box-shadow: 0px 4px 47.7px 0px rgba(0, 0, 0, 0.25);
}
.homepage__main-section {
  display: flex;
  flex-direction: column;
  gap: 80px;
  align-items: center;
  justify-content: center;
}

.homepage__main-title {
  position: relative;
  align-self: stretch;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 72px;
  letter-spacing: -0.05em;
  color: #b99c6d;
  height: 155px;
  line-height: 65.28px;
}

.homepage__main-content {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 0;
  align-items: flex-start;
  width: 797px;
  color: #3e3e3e;
  justify-content: center;
  text-align: center;
  align-items: center;
}
.homepage__main-wrapper {
  display: flex;
  padding-left: 67px;
  padding-top: 133px;
  flex-direction: column;
  height: 100vh;
  padding-bottom: 87px;
  justify-content: space-between;
  min-height: 100vh;
}
.homepage__main-quote {
  position: relative;
  align-self: stretch;
  font-family: "Josefin Sans", sans-serif;
  font-size: 32px;
  font-style: italic;
  letter-spacing: -0.05em;
  line-height: 32px;
  color: black;
  height: 209px;
}
.homepage2 {
  height: 100vh;
  width: 400vw;
  background-color: #fdfbf4;
  display: flex;
  gap: 0; /* Remove gap to ensure exact 100vw sections */
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

.feature1 {
  height: 100vh;
  width: 50vw; /* Each section is exactly 100vw */
  background-image: url("/public//feature1.png");
  background-size: cover;
  background-position: center;
  box-shadow: 0px 4px 47.7px 0px rgba(0, 0, 0, 0.25);
  flex-shrink: 0; /* Prevent shrinking */
}
.feature2 {
  height: 100vh;
  width: 50vw; /* Each section is exactly 100vw */
  background-image: url("/public//feature2.png");
  background-size: cover;
  background-position: center;
  box-shadow: 0px 4px 47.7px 0px rgba(0, 0, 0, 0.25);
  flex-shrink: 0; /* Prevent shrinking */
}
.feature4 {
  height: 100vh;
  width: 50vw; /* Each section is exactly 100vw */
  background-image: url("/public//feature4.png");
  background-size: cover;
  background-position: center;
  box-shadow: 0px 4px 47.7px 0px rgba(0, 0, 0, 0.25);
  flex-shrink: 0; /* Prevent shrinking */
}
.feature3 {
  height: 100vh;
  width: 50vw; /* Each section is exactly 100vw */
  background-image: url("/public//feature3.png");
  background-size: cover;
  background-position: center;
  box-shadow: 0px 4px 47.7px 0px rgba(0, 0, 0, 0.25);
  flex-shrink: 0; /* Prevent shrinking */
}

.homepage__content-engine-ratings {
  display: inline-flex;

  gap: 6px;
  align-items: center;
  height: 37px;

  width: 124px;
}

.homepage__content-engine-rating-star {
  object-fit: cover;
  position: relative;
  aspect-ratio: 38/37;
  height: 37px;
  width: 38px;
}

.homepage__content-engine-content {
  display: flex;
  flex-direction: column;
  gap: 56px;
  align-content: center;
  padding-left: 7vw;
  justify-content: center;
  width: 50vw; /* Each section is exactly 100vw */
  flex-shrink: 0; /* Prevent shrinking */
  z-index: 2;
}

.homepage__content-engine-title {
  position: relative;
  align-self: stretch;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 72px;
  color: #3c1f13;
  line-height: 78.39px;
}

.homepage__content-engine-title-text {
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 72px;
  color: #3c1f13;
}

.homepage__content-engine-description {
  position: relative;
  font-size: 32px;
  font-weight: 300;
  line-height: 36px;
  color: black;
  width: 480px;
}

.homepage__content-engine-description-text {
  font-size: 32px;
  color: black;
}
.homepage3 {
  height: 100vh;
  width: 100vw;
  background-color: #fdfbf4;
  display: flex;
  gap: 0; /* Remove gap to ensure exact 100vw sections */
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}
.homepage__why-us-section {
  display: flex;
  flex-direction: row;
  gap: 40px;
  position: relative;

  width: 100%;

  padding-left: 120px;
  background-color: #fdfbf4;
  justify-content: space-between;
  min-height: 100vh;
}

.homepage__why-us-title {
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 128px;
  color: #966f33;
  padding-top: 80px;
  height: 117px;
  line-height: 117px;
}

.homepage__why-us-content {
  display: flex;
  gap: 64px;
  align-items: center;
}

.homepage__why-us-image {
  object-fit: cover;
  position: absolute;
  bottom: 0;
  right: 44vw;
  aspect-ratio: 764/889;
  height: 100vh;
}

.homepage__why-us-features {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 20px;
  align-items: flex-start;
}
.homepage__feature-item-container {
  position: relative;
  width: 100%;
}

.homepage__feature-item-header {
  display: flex;
  position: relative;
  gap: 14px;
  align-items: flex-start;
  height: 42px;
}

.homepage__feature-item-icon {
  position: relative;
  width: 42px;
  height: 42px;
  flex-shrink: 0;
}

.homepage__feature-item-title {
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 36px;
  letter-spacing: -0.025em;
  line-height: 36px;
  color: #966f33;
  height: 35px;
}

.homepage__feature-item-description {
  margin-top: 12px;
  margin-left: 56px;
  font-family: "Source Sans Pro", sans-serif;
  font-size: 20px;
  line-height: 24px;
  color: black;
  width: 481px;
}
.homepage4 {
  height: 100vh;
  width: 100vw;
  background-color: #fdfbf4;
  display: flex;
  gap: 0; /* Remove gap to ensure exact 100vw sections */
  position: relative;
  overflow: hidden;
  justify-content: space-between;
  min-height: 100vh;
}
.homepage__hero2 {
  background-color: #fefaef;

  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  height: 100vh;
  width: 48vw;
  box-shadow: 0px 4px 47.7px 0px rgba(0, 0, 0, 0.25);
}
.homepage__subhero {
  background-color: #e5cca4;
  width: 50%;
  position: absolute;
  right: 0;
  height: 100%;
}
.testimonialimage {
  background-image: url("/public//testimonial.jpg");
  background-size: cover;
  background-position: center;
  z-index: 3;

  width: 744px;
  height: 732px;
  aspect-ratio: 744/732;
}

.homepage__testimonials-content {
  width: 50%;
}

.homepage__testimonials-header {
  max-width: 100%;
  line-height: 1;
  color: #5d4037;

  padding-top: 111px;
  padding-left: 162px;
}

.homepage__testimonials-title {
  font-size: 48px;
  letter-spacing: 2px;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  color: #5d4037;
}

.homepage__testimonials-quote-mark {
  margin-top: 30px;
  
  
  background-image: url("/public/qoutation.png");
  width: 50px;
  height: 40px;
  background-size: cover;
  background-position: center;
  margin-bottom: 30px;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  color: #966f33;
}

.homepage__testimonials-quote-box {
  display: flex;

  justify-content: flex-start;
  align-items: flex-start;

  width: 100%;
  font-size: 61px;
  padding-left: 162px;

  line-height: 60px;
  min-height: 435px;
  color: #a27e2d;
  letter-spacing: 2.08px;
}

.homepage__testimonials-quote-text {
  font-family: "Instrument Serif", serif;
  font-weight: 200;
}

.homepage__testimonials-navigation {
  display: flex;
  gap: 6px;
  align-items: center;
  align-self: end;
  margin-top: 32px;
}
.homepage__team-testimonial-section {
  overflow: hidden;
  position: relative;
  margin: 0 auto;
  width: 100%;
  max-width: none;
  background-color: #f5f5f1;
  height: 871px;
}

.homepage__team-testimonial-background {
  position: absolute;
  top: 0;
  left: 0;
  height: 871px;
  width: 799px;
}

.homepage__team-testimonial-content {
  display: flex;
  position: absolute;
  flex-direction: column;
  gap: 40px;
  align-items: flex-start;
  height: 367px;
  left: 841px;
  top: 255px;
  width: 491px;
}

.homepage__team-testimonial-ratings {
  display: flex;
  position: relative;
  gap: 6px;
  align-items: center;
}

.homepage__team-testimonial-star {
  position: relative;
  aspect-ratio: 38/37;
  height: 37px;
  width: 38px;
}

.homepage__team-testimonial-text-content {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 28px;
  align-items: flex-start;
  align-self: stretch;
}

.homepage__team-testimonial-title {
  position: relative;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 72px;
  letter-spacing: -0.025em;
  color: #966f33;
  line-height: 62.22px;
}

.homepage__team-testimonial-quote {
  position: relative;
  align-self: stretch;
  font-size: 20px;
  font-style: italic;
  line-height: 24px;
  color: black;
  height: 75px;
}

.homepage__team-testimonial-attribution {
  position: relative;
  height: 24px;
  width: 368px;
}

.homepage__team-testimonial-author {
  position: absolute;
  left: 0;
  top: 2px;
  height: 20px;
  font-size: 20px;
  letter-spacing: -0.025em;
  line-height: 20px;
  color: black;
}
.homepage__cta-section {
  overflow: hidden;
  position: relative;
  margin: 0 auto;
  width: 100%;
  max-width: none;
  background-color: #fafaf5;
  height: 871px;
}


.homepage__cta-content {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
}

.homepage__cta-title {
  position: relative;
  align-self: stretch;
  font-size: 96px;
  color: #650a0a;
  height: 327px;
  line-height: 99.9px;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
}

.homepage__cta-description {
  position: relative;
  font-size: 20px;
  font-weight: 300;
  letter-spacing: -0.025em;
  line-height: 20px;
  color: #71717a;
  font-family: "Source Sans Pro", sans-serif;
}

.homepage__cta-button {
  display: flex;
  position: relative;
  gap: 10px;
  justify-content: center;
  align-items: center;
  padding: 20px 40px;
  background-color: #966f33;
  cursor: pointer;
  height: 65px;
  width: 216px;
}

.homepage__cta-button-text {
  position: relative;
  font-size: 20px;
  letter-spacing: 0.05em;
  line-height: 24px;
  color: white;
  font-family: "Source Sans Pro", sans-serif;
}

.homepage__cta-image {
  position: absolute;
  top: 0;
  flex-shrink: 0;
  aspect-ratio: 1011/1190;
  height: 1190px;
  left: 1178px;
  width: 1011px;
}

.homepage__hero3 {
  background-color: #fefaef;

  position: relative;

  box-shadow: 0px 4px 47.7px 0px rgba(0, 0, 0, 0.25);

  display: flex;
  position: absolute;
  flex-direction: column;
  gap: 56px;
  align-items: flex-start;
  height: 100vh;
  width: 48vw;
  padding-top: 101px;
  padding-left:33px;

}
